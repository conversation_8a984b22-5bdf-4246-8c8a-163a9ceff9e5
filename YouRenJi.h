#ifndef YOURENJI_H
#define YOURENJI_H

#include <QMainWindow>
#include <QTimer>
#include <QThread>
#include <QQueue>
#include <QMutex>
#include <QSerialPort>
#include <QMessageBox>
#include "YouRenJiWorker.h"

namespace Ui {
class YouRenJi;
}

/**
 * @brief 有人机模块主窗口类
 * 
 * 该类负责有人机功能的实现，包括：
 * - UDP通信管理
 * - 串口通信管理
 * - 性能监控和统计
 * - 日志记录和显示
 */
class YouRenJi : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit YouRenJi(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * 负责清理资源，包括工作线程、定时器等
     */
    ~YouRenJi();

private slots:
    /**
     * @brief 连接/断开连接按钮点击处理
     */
    void onConnect();

    /**
     * @brief 处理一般消息
     * @param message 待处理的消息
     */
    void handleMessage(const QString &message);

    /**
     * @brief 处理错误消息
     * @param error 错误信息
     */
    void handleError(const QString &error);

    /**
     * @brief 更新连接状态
     * @param connected 是否已连接
     */
    void updateConnectionStatus(bool connected);

    /**
     * @brief 更新日志显示
     * 将缓冲区中的日志消息显示到界面上
     */
    void updateLogDisplay();

    /**
     * @brief 刷新可用串口列表
     */
    void refreshComPorts();

    /**
     * @brief 串口开关按钮点击处理
     */
    void onComPortToggle();

    /**
     * @brief 处理串口错误
     * @param error 错误信息
     */
    void handleSerialError(const QString &error);

private:
    /**
     * @brief 初始化UI组件
     * 设置日志显示、输入验证等
     */
    void initUiComponents();

    /**
     * @brief 初始化串口设置
     * 配置串口参数和刷新定时器
     */
    void initSerialPortSettings();

    /**
     * @brief 配置串口参数
     * 应用当前选择的串口设置
     */
    void configureSerialPort();

private:
    Ui::YouRenJi *ui;                 ///< UI对象指针
    bool connectState = false;         ///< 连接状态
    bool serialPortOpen = false;       ///< 串口打开状态
    
    QThread workerThread;             ///< 工作线程
    YouRenJiWorker *worker;           ///< 消息处理工作对象
    QTimer *logUpdateTimer;           ///< 日志更新定时器
    QTimer *comRefreshTimer;          ///< 串口刷新定时器
    QQueue<QString> logBuffer;        ///< 日志缓冲区
    QMutex logMutex;                 ///< 日志互斥锁
    
    QSerialPort *serialPort;          ///< 串口对象
    
    static const int MAX_LOG_BUFFER = 1024;        ///< 最大日志缓冲区大小
    static const int LOG_UPDATE_INTERVAL = 200;    ///< 日志更新间隔（毫秒）
};

#endif // YOURENJI_H
