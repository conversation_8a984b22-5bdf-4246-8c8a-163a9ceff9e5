QT += core network serialport
QT -= gui

CONFIG += c++17 console
CONFIG -= app_bundle

TARGET = test_yourenji
TEMPLATE = app

SOURCES += \
    test_multithreaded_yourenji.cpp \
    YouRenJiWorker.cpp

HEADERS += \
    YouRenJiWorker.h

# Compiler flags for better debugging and warnings
QMAKE_CXXFLAGS += -Wall -Wextra -std=c++17

# Debug configuration
CONFIG(debug, debug|release) {
    QMAKE_CXXFLAGS += -g -O0
    DEFINES += DEBUG
}

# Release configuration
CONFIG(release, debug|release) {
    QMAKE_CXXFLAGS += -O2 -DNDEBUG
}

# Platform-specific settings
win32 {
    QMAKE_CXXFLAGS += -D_WIN32_WINNT=0x0601
}

unix {
    QMAKE_CXXFLAGS += -pthread
    LIBS += -pthread
}
