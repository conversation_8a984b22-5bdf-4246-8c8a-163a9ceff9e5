#include "YouRenJiWorker.h"
#include <QCoreApplication>
#include <QTimer>
#include <QDebug>
#include <QThread>
#include <QElapsedTimer>

/**
 * @brief Simple test application to validate the multi-threaded YouRenJiWorker implementation
 */
class YouRenJiTest : public QObject
{
    Q_OBJECT

public:
    YouRenJiTest(QObject* parent = nullptr) : QObject(parent), worker(nullptr) {}

    void runTests() {
        qDebug() << "=== YouRenJi Multi-threaded Architecture Test ===";
        
        // Test 1: Basic initialization and cleanup
        testBasicInitialization();
        
        // Test 2: Thread-safe queue functionality
        testThreadSafeQueues();
        
        // Test 3: Worker thread lifecycle
        testWorkerThreadLifecycle();
        
        // Test 4: Resource cleanup
        testResourceCleanup();
        
        qDebug() << "=== All tests completed ===";
        QCoreApplication::quit();
    }

private slots:
    void onMessageReceived(const QString& message) {
        qDebug() << "Message:" << message;
    }
    
    void onErrorOccurred(const QString& error) {
        qDebug() << "Error:" << error;
    }
    
    void onConnectionStatusChanged(bool connected) {
        qDebug() << "Connection status changed:" << connected;
    }

private:
    void testBasicInitialization() {
        qDebug() << "\n--- Test 1: Basic Initialization ---";
        
        worker = new YouRenJiWorker(this);
        
        // Connect signals for monitoring
        connect(worker, &YouRenJiWorker::messageReceived,
                this, &YouRenJiTest::onMessageReceived);
        connect(worker, &YouRenJiWorker::errorOccurred,
                this, &YouRenJiTest::onErrorOccurred);
        connect(worker, &YouRenJiWorker::connectionStatusChanged,
                this, &YouRenJiTest::onConnectionStatusChanged);
        
        // Set up basic configuration
        worker->setAddresses("*********", "12321", "*********", "7021");
        worker->setSerialPortSettings("COM1", 115200, 
                                     QSerialPort::Data8, 
                                     QSerialPort::NoParity, 
                                     QSerialPort::OneStop);
        
        qDebug() << "✓ Basic initialization completed";
    }
    
    void testThreadSafeQueues() {
        qDebug() << "\n--- Test 2: Thread-Safe Queue Functionality ---";
        
        // Test ThreadSafeQueue directly
        ThreadSafeQueue<ThreadMessage> testQueue(100);
        
        // Test basic enqueue/dequeue
        ThreadMessage testMsg(ThreadMessage::UdpData, QByteArray("test data"));
        
        bool enqueueResult = testQueue.tryEnqueue(testMsg);
        qDebug() << "✓ Enqueue test:" << (enqueueResult ? "PASS" : "FAIL");
        
        ThreadMessage retrievedMsg;
        bool dequeueResult = testQueue.tryDequeue(retrievedMsg);
        qDebug() << "✓ Dequeue test:" << (dequeueResult ? "PASS" : "FAIL");
        
        // Test queue size and empty state
        qDebug() << "✓ Queue size after dequeue:" << testQueue.size();
        qDebug() << "✓ Queue empty state:" << testQueue.isEmpty();
        
        // Test queue shutdown
        testQueue.shutdown();
        qDebug() << "✓ Queue shutdown test:" << (testQueue.isShutdown() ? "PASS" : "FAIL");
    }
    
    void testWorkerThreadLifecycle() {
        qDebug() << "\n--- Test 3: Worker Thread Lifecycle ---";
        
        if (!worker) {
            qDebug() << "✗ Worker not initialized";
            return;
        }
        
        QElapsedTimer timer;
        timer.start();
        
        // Start the worker (this will initialize threads)
        worker->start();
        qDebug() << "✓ Worker start initiated";
        
        // Let it run for a short time
        QThread::msleep(1000);
        
        // Stop the worker
        worker->stop();
        qDebug() << "✓ Worker stop initiated";
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "✓ Lifecycle test completed in" << elapsed << "ms";
    }
    
    void testResourceCleanup() {
        qDebug() << "\n--- Test 4: Resource Cleanup ---";
        
        if (worker) {
            delete worker;
            worker = nullptr;
            qDebug() << "✓ Worker cleanup completed";
        }
        
        // Test memory usage (basic check)
        qDebug() << "✓ Resource cleanup test completed";
    }

private:
    YouRenJiWorker* worker;
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    YouRenJiTest test;
    
    // Run tests after event loop starts
    QTimer::singleShot(100, &test, &YouRenJiTest::runTests);
    
    return app.exec();
}

#include "test_multithreaded_yourenji.moc"
