# YouRenJiWorker Multi-threaded Architecture

## Overview

The YouRenJiWorker has been refactored to implement a multi-threaded producer-consumer pattern with four distinct threads for improved performance, reliability, and maintainability.

## Architecture Design

### Thread 1: Serial to UDP Producer (SerialToUdpWorker)
- **Purpose**: Receives messages from the serial port and sends them to UDP multicast
- **Target**: *********:12321
- **Responsibilities**:
  - Monitor serial port for incoming data
  - Validate serial packet format
  - Forward specific packet types to UDP multicast
  - Handle serial communication errors

### Thread 2: UDP to Queue Producer (UdpToQueueWorker)
- **Purpose**: Receives UDP messages and places them into a thread-safe message queue
- **Listen Address**: *********:7021
- **Responsibilities**:
  - Bind to UDP socket and listen for incoming messages
  - Validate incoming UDP packets
  - Enqueue messages into thread-safe queue for processing
  - Handle UDP communication errors

### Thread 3: Queue Consumer and Frame Processor (QueueConsumerWorker)
- **Purpose**: Processes queued messages and handles EO frame replacement
- **EO Listen Address**: *********:8000
- **Responsibilities**:
  - Consume messages from the thread-safe queue
  - Receive EO data via UDP
  - Replace the 4th frame (index 3) with EO frame data for 256-byte messages
  - Write processed messages to serial port
  - Handle chunked transmission for large messages

### Thread 4: Main Coordination Thread (YouRenJiWorker)
- **Purpose**: Coordinates all worker threads and maintains API compatibility
- **Responsibilities**:
  - Initialize and manage worker thread lifecycle
  - Provide unified error handling and logging
  - Maintain backward compatibility with existing API
  - Handle graceful shutdown and resource cleanup

## Key Components

### ThreadSafeQueue<T>
Enhanced thread-safe queue implementation with:
- Blocking and non-blocking operations
- Timeout support for operations
- Size limits with overflow handling
- Graceful shutdown mechanism
- Condition variables for efficient waiting

### ThreadMessage Structure
```cpp
struct ThreadMessage {
    enum Type { SerialData, UdpData, EOData, ControlCommand, Shutdown };
    Type type;
    QByteArray data;
    QString sourceAddress;
    quint16 sourcePort;
    qint64 timestamp;
};
```

## Thread Synchronization

### Atomic Operations
- `QAtomicInt isRunning`: Thread-safe running state
- `QAtomicInt activeThreadCount`: Track active worker threads

### Mutex Protection
- `stateMutex`: Protects shared state variables
- `threadManagementMutex`: Protects thread lifecycle operations
- Individual worker mutexes for thread-specific operations

### Condition Variables
- `threadsFinished`: Signals when all threads have completed
- Queue-specific conditions for producer-consumer coordination

## Message Flow

```
Serial Port → Thread 1 → UDP Multicast (*********:12321)

UDP (*********:7021) → Thread 2 → Message Queue → Thread 3 → Serial Port
                                                      ↑
EO Data (*********:8000) ────────────────────────────┘
```

## Error Handling

### Graceful Degradation
- Individual thread failures don't crash the entire system
- Automatic retry mechanisms for transient errors
- Comprehensive error logging with timestamps

### Resource Management
- Automatic cleanup of thread resources
- Timeout-based thread termination
- Memory leak prevention through RAII patterns

## Performance Optimizations

### Queue Management
- Configurable queue sizes (default: 5000 messages)
- Non-blocking operations where possible
- Efficient memory usage with move semantics

### Network Optimization
- Large UDP buffer sizes (32MB)
- Low-delay socket options
- Chunked transmission for large messages

### Thread Priorities
- High priority for time-critical operations
- Balanced CPU usage across threads

## Backward Compatibility

The refactored implementation maintains full backward compatibility:
- All existing public methods remain unchanged
- Same signal/slot interface
- Identical configuration methods
- Legacy thread support (deprecated but functional)

## Configuration

### Thread Queue Sizes
```cpp
static const int THREAD_QUEUE_SIZE = 5000;
static const int THREAD_TIMEOUT_MS = 100;
static const int THREAD_SHUTDOWN_TIMEOUT_MS = 5000;
```

### Network Addresses
- Serial to UDP: *********:12321
- UDP to Queue: *********:7021  
- EO Data: *********:8000

## Usage Example

```cpp
YouRenJiWorker worker;

// Configure addresses and serial port
worker.setAddresses("*********", "12321", "*********", "7021");
worker.setSerialPortSettings("COM1", 115200, QSerialPort::Data8, 
                            QSerialPort::NoParity, QSerialPort::OneStop);

// Start multi-threaded processing
worker.start();

// ... application logic ...

// Graceful shutdown
worker.stop();
```

## Testing

Use the provided test application to validate:
- Thread lifecycle management
- Message queue functionality
- Resource cleanup
- Error handling scenarios

```bash
qmake test_yourenji.pro
make
./test_yourenji
```

## Benefits

1. **Improved Performance**: Parallel processing of serial and UDP data
2. **Better Reliability**: Isolated thread failures don't affect the entire system
3. **Enhanced Maintainability**: Clear separation of concerns
4. **Scalability**: Easy to add new processing threads
5. **Resource Efficiency**: Optimized memory and CPU usage
